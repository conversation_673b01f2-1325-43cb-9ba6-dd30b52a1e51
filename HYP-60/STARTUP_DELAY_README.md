# 启动延迟功能说明

## 功能概述

为了避免多个传感器设备同时开机时造成OpenThread网络拥塞，我们实现了基于EUI64地址的智能启动延迟功能。

## 延迟算法

### 1. 稳定分散延迟 (0~8000ms)
- 基于设备的EUI64地址计算
- 每个设备有唯一且稳定的延迟时间
- 确保相同设备每次启动的延迟时间一致

### 2. 随机延迟 (0~2000ms)
- 使用线性同余生成器
- 种子基于EUI64地址
- 增加额外的随机性

### 3. 总延迟范围
- 最小延迟：0ms
- 最大延迟：10000ms (10秒)
- 平均延迟：约5秒

## 实现位置

延迟函数在 `CurrentSensor_task()` 的开始处调用，在以下操作之前：
- 传感器初始化 (`sensor_init()`)
- OpenThread栈创建
- 网络连接建立

## 调试功能

### 启用调试模式
在 `currentsensor.c` 文件中取消注释以下行：
```c
#define DEBUG_STARTUP_DELAY
```

### LED指示说明
当启用调试模式时：
1. **快速闪烁**：表示延迟开始，闪烁次数 = 延迟秒数 + 1
2. **长闪烁**：表示延迟结束，500ms长闪

## 代码位置

- **函数声明**：第270行附近
- **函数调用**：`CurrentSensor_task()` 函数开始处（第2062行）
- **函数实现**：文件末尾（第2240行开始）

## 测试验证

1. 编译并烧录固件到多个设备
2. 同时给多个设备上电
3. 观察设备启动时间的分散情况
4. 如果启用了调试模式，观察LED闪烁模式

## 注意事项

1. 延迟在网络初始化之前执行，不会影响已建立的连接
2. 延迟时间基于EUI64计算，确保设备间的良好分散
3. 调试模式仅用于开发阶段，生产环境建议关闭
4. 延迟不会影响从shutdown模式唤醒的设备

## 性能影响

- **内存占用**：约100字节代码空间
- **启动时间**：增加0~10秒延迟
- **运行时性能**：无影响（仅在启动时执行一次）
